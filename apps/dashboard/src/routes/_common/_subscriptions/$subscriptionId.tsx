import { subscriptionsApi } from '@mass/api'
import { subscription$ } from '@mass/components/dashboard'
import { createFileRoute } from '@tanstack/react-router'
import clsx from 'clsx'
import type { FC } from 'react'

interface TabItem {
  label: string
  isActive?: boolean
}

const TabsComponent: FC<{ tabs: TabItem[] }> = ({ tabs }) => {
  return (
    <div className='border-b border-gray-200'>
      <nav className='flex space-x-8' aria-label='Tabs'>
        {tabs.map(tab => {
          return (
            <button
              key={tab.label}
              type='button'
              className={clsx(
                'border-b-2 px-1 py-4 font-medium text-sm whitespace-nowrap transition-colors duration-200',
                tab.isActive
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
              )}>
              {tab.label}
            </button>
          )
        })}
      </nav>
    </div>
  )
}

function Subscription() {
  const tabs: TabItem[] = [
    { label: 'Genel Bakış', isActive: true },
    { label: 'Faturalar', isActive: false },
    { label: 'Tüketim', isActive: false },
    { label: 'Ayarlar', isActive: false },
  ]

  return (
    <div className='p-16'>
      <TabsComponent tabs={tabs} />
      <div className='mt-8'>
        <div className='grid gap-8 md:grid-cols-2 xl:grid-cols-3'>sa</div>
      </div>
    </div>
  )
}

export const Route = createFileRoute('/_common/_subscriptions/$subscriptionId')({
  async beforeLoad({ params }) {
    const subscriptionId = params.subscriptionId

    const subscription = await subscriptionsApi.detail({
      query: {
        id: subscriptionId,
      },
    })

    subscription$.selectedSubscription.set(subscription)
  },

  component: Subscription,
})
