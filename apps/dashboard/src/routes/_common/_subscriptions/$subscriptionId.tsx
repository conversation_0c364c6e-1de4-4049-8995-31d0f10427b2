import { subscriptionsApi } from '@mass/api'
import { subscription$ } from '@mass/components/dashboard'
import { createFileRoute } from '@tanstack/react-router'

function Subscription() {
  return <div className='grid gap-8 p-16 md:grid-cols-2 xl:grid-cols-3'>sa</div>
}

export const Route = createFileRoute('/_common/_subscriptions/$subscriptionId')({
  async beforeLoad({ params }) {
    const subscriptionId = params.subscriptionId

    const subscription = await subscriptionsApi.detail({
      query: {
        id: subscriptionId,
      },
    })

    subscription$.selectedSubscription.set(subscription)
  },

  component: Subscription,
})
