import { type } from 'arktype'

import { apiBuilder } from '../utils/api-builder'

const subscriptionContent = type({
  id: 'string',
  createdAt: 'string',
  name: 'string',
  userId: 'string',
  type: "'electricity-production' | 'electricity-consumption'",
  individual: 'boolean',
  personIdentifier: type.string.atLeastLength(10).atMostLength(10),
  regionId: 'string',
  installationId: 'string',
  startDate: 'string',
  key: 'string',
  deletedAt: 'null',
  userDefinedLimit: 'null',
  unexpectedUsageThreshold: 'number',
})

export const newSubscriptionPayload = subscriptionContent
  .pick('name', 'individual', 'personIdentifier', 'regionId', 'installationId')
  .required()

export const updateSubscriptionPayload = subscriptionContent.pick('name').required()

export const subscriptionsApi = {
  regions: apiBuilder({
    url: '/region',
    cache: 'until-reload',

    response: type({
      id: 'string',
      name: 'string',
    }).array(),
  }),

  list: apiBuilder({
    url: '/subscription',
    cache: 'validate',

    params: type({
      'filter:eq?': subscriptionContent.partial().array(),
    }),

    encodeParamsKeysUrl: ['filter:eq'],

    response: type({
      content: subscriptionContent.array(),
      pageable: {
        pageNumber: 'number',
        pageSize: 'number',
        sort: {
          sorted: 'boolean',
          empty: 'boolean',
          unsorted: 'boolean',
        },
        offset: 'number',
        paged: 'boolean',
        unpaged: 'boolean',
      },
      last: 'boolean',
      totalPages: 'number',
      totalElements: 'number',
      size: 'number',
      number: 'number',
      sort: {
        sorted: 'boolean',
        empty: 'boolean',
        unsorted: 'boolean',
      },
      first: 'boolean',
      numberOfElements: 'number',
      empty: 'boolean',
    }),
  }),

  create: apiBuilder({
    method: 'POST',
    url: '/subscription',

    payload: newSubscriptionPayload,

    response: type('unknown'),
  }),

  update: apiBuilder({
    method: 'PATCH',
    url: '/subscription/$id',

    query: type({
      id: 'string',
    }),

    payload: updateSubscriptionPayload,

    response: type('unknown'),
  }),

  delete: apiBuilder({
    method: 'DELETE',
    url: '/subscription/$id',

    query: type({
      id: 'string',
    }),

    response: type('unknown'),
  }),

  detail: apiBuilder({
    url: '/subscription/$id',

    query: type({
      id: 'string',
    }),

    response: subscriptionContent,
  }),
} as const
