declare global {
  namespace Api {
    namespace Stores {
      interface Global {
        user: Api.ExtractResponse<Api.Services['auth']['me']> | null
        isUserValid: () => boolean

        subscription: {
          regions: Api.ExtractResponse<Api.Services['subscriptions']['regions']> | null
          subscriptions: Api.ExtractResponse<Api.Services['subscriptions']['list']> | null
        }

        aggreements: {
          pdpl: boolean
          userAgreement: boolean
        }

        documents: {
          pdfs: {
            agreement: string
            kvkk: string
            about: string
            userGuide: string
          } | null
        }

        language: null | 'tr' | 'en'
      }
    }
  }
}
