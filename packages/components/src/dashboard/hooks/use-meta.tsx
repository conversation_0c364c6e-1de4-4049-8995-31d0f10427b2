import { use$ } from '@legendapp/state/react'
import { useLocation } from '@tanstack/react-router'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { subscription$ } from '../stores/subscription'

export const HOME_REGEX = /^\/$/
export const SUBSCRIPTION_REGEX = /^\/[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}/

export const useMeta = () => {
  const { pathname } = useLocation()
  const { t: dashboard } = useTranslation('dashboard')

  const selectedSubscription = use$(() => subscription$.selectedSubscription.get())

  return useMemo(() => {
    const routes = [
      [HOME_REGEX, 'subscriptions.title', 'subscriptions.description', false],
      [
        SUBSCRIPTION_REGEX,
        dashboard('subscriptions.x', {
          name: selectedSubscription?.name ?? '',
        }),
        dashboard('subscriptions.x-detail'),
        false,
      ],
    ] as const

    const foundRoute = routes.find(([regex]) => regex.test(pathname))

    if (!foundRoute) {
      return {
        title: null,
        description: null,
        hasTabs: false,
      }
    }

    return {
      title: dashboard(foundRoute[1]),
      description: dashboard(foundRoute[2]),
      hasTabs: foundRoute[3],
    }
  }, [pathname, dashboard, selectedSubscription])
}
